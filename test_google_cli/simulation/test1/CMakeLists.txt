cmake_minimum_required(VERSION 3.10)
project(worldsim VERSION 0.1 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(OpenGL REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(GLFW REQUIRED glfw3)

include_directories(${OPENGL_INCLUDE_DIRS} ${GLFW_CFLAGS_OTHER})
include_directories(${GLFW_INCLUDE_DIRS})
include_directories(${CMAKE_SOURCE_DIR}/src)

add_executable(worldsim
    src/main.cpp
    src/World.cpp
    src/Renderer.cpp
)

target_compile_definitions(worldsim PRIVATE GL_GLEXT_PROTOTYPES)

target_include_directories(worldsim PRIVATE ${GLFW_INCLUDE_DIRS})
target_link_libraries(worldsim ${OPENGL_LIBRARIES} ${GLFW_LIBRARIES})

# Ensure math constants available
add_definitions(-DGLM_FORCE_RADIANS)
