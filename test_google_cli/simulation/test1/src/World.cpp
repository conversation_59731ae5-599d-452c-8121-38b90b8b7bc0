#include "World.h"
#include <cmath>

World::World() {
    // simple scene: a few bodies
    m_bodies.push_back({{ -1.0f, 0.0f, -5.0f }, { 0.5f, 0.2f, 0.0f }, 0.2f, 1.0f});
    m_bodies.push_back({{ 1.0f, 0.0f, -5.5f }, { -0.3f, 0.0f, 0.0f }, 0.3f, 2.0f});
    m_bodies.push_back({{ 0.0f, 0.8f, -4.5f }, { 0.0f, -0.4f, 0.0f }, 0.15f, 0.5f});
}

void World::update(double dt) {
    // simple Euler integration + gravity + boundary bounce
    const glm::vec3 gravity(0.0f, -1.0f, 0.0f);
    for (auto &b : m_bodies) {
        b.velocity += gravity * static_cast<float>(dt);
        b.position += b.velocity * static_cast<float>(dt);

        // ground plane at y = -1.0
        if (b.position.y - b.radius < -1.0f) {
            b.position.y = -1.0f + b.radius;
            b.velocity.y = -b.velocity.y * 0.7f; // restitution
        }

        // simple x bounds
        if (b.position.x - b.radius < -3.0f) {
            b.position.x = -3.0f + b.radius;
            b.velocity.x = -b.velocity.x * 0.8f;
        }
        if (b.position.x + b.radius > 3.0f) {
            b.position.x = 3.0f - b.radius;
            b.velocity.x = -b.velocity.x * 0.8f;
        }
    }
}
