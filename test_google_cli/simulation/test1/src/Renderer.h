#pragma once

#include "World.h"

class Renderer {
public:
    Renderer();
    ~Renderer();
    void render(const World& world, int width, int height);
private:
    unsigned int compileShader(unsigned int type, const char* src);
    unsigned int createProgram(const char* vsSrc, const char* fsSrc);

    unsigned int m_program = 0;
    unsigned int m_vao = 0;
    unsigned int m_vbo = 0;
    unsigned int m_ebo = 0;
    int m_indexCount = 0;
};
