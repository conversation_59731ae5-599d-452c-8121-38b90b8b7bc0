#include "Renderer.h"
#include <GL/gl.h>
#include <GL/glext.h>
#include <GLFW/glfw3.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

#include <vector>
#include <iostream>
#include <cmath>

#include <cstring>

// We'll load required modern GL function pointers at runtime using glfwGetProcAddress.
// Declare function pointer types (from glext) and local pointers prefixed with 'p'.
static PFNGLCREATESHADERPROC pglCreateShader = nullptr;
static PFNGLSHADERSOURCEPROC pglShaderSource = nullptr;
static PFNGLCOMPILESHADERPROC pglCompileShader = nullptr;
static PFNGLGETSHADERIVPROC pglGetShaderiv = nullptr;
static PFNGLGETSHADERINFOLOGPROC pglGetShaderInfoLog = nullptr;
static PFNGLCREATEPROGRAMPROC pglCreateProgram = nullptr;
static PFNGLATTACHSHADERPROC pglAttachShader = nullptr;
static PFNGLLINKPROGRAMPROC pglLinkProgram = nullptr;
static PFNGLGETPROGRAMIVPROC pglGetProgramiv = nullptr;
static PFNGLGETPROGRAMINFOLOGPROC pglGetProgramInfoLog = nullptr;
static PFNGLDELETESHADERPROC pglDeleteShader = nullptr;
static PFNGLUSEPROGRAMPROC pglUseProgram = nullptr;
static PFNGLGETUNIFORMLOCATIONPROC pglGetUniformLocation = nullptr;
static PFNGLUNIFORMMATRIX4FVPROC pglUniformMatrix4fv = nullptr;
static PFNGLUNIFORM3FPROC pglUniform3f = nullptr;
static PFNGLGENVERTEXARRAYSPROC pglGenVertexArrays = nullptr;
static PFNGLBINDVERTEXARRAYPROC pglBindVertexArray = nullptr;
static PFNGLGENBUFFERSPROC pglGenBuffers = nullptr;
static PFNGLBINDBUFFERPROC pglBindBuffer = nullptr;
static PFNGLBUFFERDATAPROC pglBufferData = nullptr;
static PFNGLBUFFERSUBDATAPROC pglBufferSubData = nullptr;
static PFNGLENABLEVERTEXATTRIBARRAYPROC pglEnableVertexAttribArray = nullptr;
static PFNGLVERTEXATTRIBPOINTERPROC pglVertexAttribPointer = nullptr;
static PFNGLDELETEBUFFERSPROC pglDeleteBuffers = nullptr;
static PFNGLDELETEVERTEXARRAYSPROC pglDeleteVertexArrays = nullptr;
static PFNGLDELETEPROGRAMPROC pglDeleteProgram = nullptr;

static bool loadGLFunctions() {
    if (pglCreateShader) return true; // already loaded
    pglCreateShader = (PFNGLCREATESHADERPROC)glfwGetProcAddress("glCreateShader");
    pglShaderSource = (PFNGLSHADERSOURCEPROC)glfwGetProcAddress("glShaderSource");
    pglCompileShader = (PFNGLCOMPILESHADERPROC)glfwGetProcAddress("glCompileShader");
    pglGetShaderiv = (PFNGLGETSHADERIVPROC)glfwGetProcAddress("glGetShaderiv");
    pglGetShaderInfoLog = (PFNGLGETSHADERINFOLOGPROC)glfwGetProcAddress("glGetShaderInfoLog");
    pglCreateProgram = (PFNGLCREATEPROGRAMPROC)glfwGetProcAddress("glCreateProgram");
    pglAttachShader = (PFNGLATTACHSHADERPROC)glfwGetProcAddress("glAttachShader");
    pglLinkProgram = (PFNGLLINKPROGRAMPROC)glfwGetProcAddress("glLinkProgram");
    pglGetProgramiv = (PFNGLGETPROGRAMIVPROC)glfwGetProcAddress("glGetProgramiv");
    pglGetProgramInfoLog = (PFNGLGETPROGRAMINFOLOGPROC)glfwGetProcAddress("glGetProgramInfoLog");
    pglDeleteShader = (PFNGLDELETESHADERPROC)glfwGetProcAddress("glDeleteShader");
    pglUseProgram = (PFNGLUSEPROGRAMPROC)glfwGetProcAddress("glUseProgram");
    pglGetUniformLocation = (PFNGLGETUNIFORMLOCATIONPROC)glfwGetProcAddress("glGetUniformLocation");
    pglUniformMatrix4fv = (PFNGLUNIFORMMATRIX4FVPROC)glfwGetProcAddress("glUniformMatrix4fv");
    pglUniform3f = (PFNGLUNIFORM3FPROC)glfwGetProcAddress("glUniform3f");
    pglGenVertexArrays = (PFNGLGENVERTEXARRAYSPROC)glfwGetProcAddress("glGenVertexArrays");
    pglBindVertexArray = (PFNGLBINDVERTEXARRAYPROC)glfwGetProcAddress("glBindVertexArray");
    pglGenBuffers = (PFNGLGENBUFFERSPROC)glfwGetProcAddress("glGenBuffers");
    pglBindBuffer = (PFNGLBINDBUFFERPROC)glfwGetProcAddress("glBindBuffer");
    pglBufferData = (PFNGLBUFFERDATAPROC)glfwGetProcAddress("glBufferData");
    pglBufferSubData = (PFNGLBUFFERSUBDATAPROC)glfwGetProcAddress("glBufferSubData");
    pglEnableVertexAttribArray = (PFNGLENABLEVERTEXATTRIBARRAYPROC)glfwGetProcAddress("glEnableVertexAttribArray");
    pglVertexAttribPointer = (PFNGLVERTEXATTRIBPOINTERPROC)glfwGetProcAddress("glVertexAttribPointer");
    pglDeleteBuffers = (PFNGLDELETEBUFFERSPROC)glfwGetProcAddress("glDeleteBuffers");
    pglDeleteVertexArrays = (PFNGLDELETEVERTEXARRAYSPROC)glfwGetProcAddress("glDeleteVertexArrays");
    pglDeleteProgram = (PFNGLDELETEPROGRAMPROC)glfwGetProcAddress("glDeleteProgram");
    // glDrawElements is part of the core API and available directly.

    // basic validation
    return pglCreateShader && pglShaderSource && pglCompileShader && pglCreateProgram && pglUseProgram;
}

static const char* vertexShaderSrc = R"GLSL(
#version 330 core
layout(location = 0) in vec3 aPos;
layout(location = 1) in vec3 aNormal;

uniform mat4 uModel;
uniform mat4 uView;
uniform mat4 uProj;

out vec3 vNormal;
out vec3 vPos;

void main() {
    vNormal = mat3(transpose(inverse(uModel))) * aNormal;
    vPos = vec3(uModel * vec4(aPos, 1.0));
    gl_Position = uProj * uView * vec4(vPos, 1.0);
}
)GLSL";

static const char* fragmentShaderSrc = R"GLSL(
#version 330 core
in vec3 vNormal;
in vec3 vPos;
out vec4 FragColor;

uniform vec3 uLightPos;
uniform vec3 uColor;

void main() {
    vec3 N = normalize(vNormal);
    vec3 L = normalize(uLightPos - vPos);
    float diff = max(dot(N, L), 0.0);
    vec3 diffuse = diff * uColor;
    vec3 ambient = 0.2 * uColor;
    FragColor = vec4(ambient + diffuse, 1.0);
}
)GLSL";

// helper: generate a UV sphere mesh
static void createSphereMesh(std::vector<float>& vertices, std::vector<unsigned int>& indices, int lat = 16, int lon = 16) {
    vertices.clear();
    indices.clear();
    for (int i = 0; i <= lat; ++i) {
        float theta = (float)i / lat * M_PI;
        float sinTheta = sin(theta);
        float cosTheta = cos(theta);
        for (int j = 0; j <= lon; ++j) {
            float phi = (float)j / lon * 2.0f * M_PI;
            float sinPhi = sin(phi);
            float cosPhi = cos(phi);
            float x = cosPhi * sinTheta;
            float y = cosTheta;
            float z = sinPhi * sinTheta;
            // position
            vertices.push_back(x);
            vertices.push_back(y);
            vertices.push_back(z);
            // normal
            vertices.push_back(x);
            vertices.push_back(y);
            vertices.push_back(z);
        }
    }
    for (int i = 0; i < lat; ++i) {
        for (int j = 0; j < lon; ++j) {
            int first = i * (lon + 1) + j;
            int second = first + lon + 1;
            indices.push_back(first);
            indices.push_back(second);
            indices.push_back(first + 1);

            indices.push_back(second);
            indices.push_back(second + 1);
            indices.push_back(first + 1);
        }
    }
}

Renderer::Renderer() {
    // init GLEW for function pointers
    if (!loadGLFunctions()) {
        std::cerr << "Failed to load GL function pointers. Make sure OpenGL context is current and supports required version.\n";
    }

    glEnable(GL_DEPTH_TEST);
    glDepthFunc(GL_LESS);
    glEnable(GL_CULL_FACE);

    m_program = createProgram(vertexShaderSrc, fragmentShaderSrc);

    std::vector<float> vertices;
    std::vector<unsigned int> indices;
    createSphereMesh(vertices, indices, 20, 20);
    m_indexCount = static_cast<int>(indices.size());

    pglGenVertexArrays(1, &m_vao);
    pglBindVertexArray(m_vao);

    pglGenBuffers(1, &m_vbo);
    pglBindBuffer(GL_ARRAY_BUFFER, m_vbo);
    pglBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_STATIC_DRAW);

    pglGenBuffers(1, &m_ebo);
    pglBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_ebo);
    pglBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(unsigned int), indices.data(), GL_STATIC_DRAW);

    // positions
    pglEnableVertexAttribArray(0);
    pglVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)0);
    // normals
    pglEnableVertexAttribArray(1);
    pglVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)(3 * sizeof(float)));

    pglBindVertexArray(0);
}

Renderer::~Renderer() {
    if (m_vbo) pglDeleteBuffers(1, &m_vbo);
    if (m_ebo) pglDeleteBuffers(1, &m_ebo);
    if (m_vao) pglDeleteVertexArrays(1, &m_vao);
    if (m_program) pglDeleteProgram(m_program);
}

unsigned int Renderer::compileShader(unsigned int type, const char* src) {
    unsigned int id = pglCreateShader(type);
    pglShaderSource(id, 1, &src, nullptr);
    pglCompileShader(id);
    int success;
    pglGetShaderiv(id, GL_COMPILE_STATUS, &success);
    if (!success) {
        char buf[1024];
        pglGetShaderInfoLog(id, 1024, nullptr, buf);
        std::cerr << "Shader compile error: " << buf << "\n";
    }
    return id;
}

unsigned int Renderer::createProgram(const char* vsSrc, const char* fsSrc) {
    unsigned int vs = compileShader(GL_VERTEX_SHADER, vsSrc);
    unsigned int fs = compileShader(GL_FRAGMENT_SHADER, fsSrc);
    unsigned int prog = pglCreateProgram();
    pglAttachShader(prog, vs);
    pglAttachShader(prog, fs);
    pglLinkProgram(prog);
    int success;
    pglGetProgramiv(prog, GL_LINK_STATUS, &success);
    if (!success) {
        char buf[1024];
        pglGetProgramInfoLog(prog, 1024, nullptr, buf);
        std::cerr << "Program link error: " << buf << "\n";
    }
    pglDeleteShader(vs);
    pglDeleteShader(fs);
    return prog;
}

void Renderer::render(const World& world, int width, int height) {
    glViewport(0, 0, width, height);
    glClearColor(0.1f, 0.12f, 0.15f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    pglUseProgram(m_program);

    glm::mat4 proj = glm::perspective(glm::radians(60.0f), width > 0 ? (float)width / height : 4.0f/3.0f, 0.1f, 100.0f);
    glm::mat4 view = glm::lookAt(glm::vec3(0.0f, 1.5f, 2.5f), glm::vec3(0.0f, 0.0f, -5.0f), glm::vec3(0.0f, 1.0f, 0.0f));

    int locProj = pglGetUniformLocation(m_program, "uProj");
    int locView = pglGetUniformLocation(m_program, "uView");
    int locModel = pglGetUniformLocation(m_program, "uModel");
    int locLight = pglGetUniformLocation(m_program, "uLightPos");
    int locColor = pglGetUniformLocation(m_program, "uColor");

    pglUniformMatrix4fv(locProj, 1, GL_FALSE, &proj[0][0]);
    pglUniformMatrix4fv(locView, 1, GL_FALSE, &view[0][0]);

    pglUniform3f(locLight, 2.0f, 5.0f, 2.0f);

    // draw ground as simple quad using fixed pipeline fallback via shader
    glm::mat4 groundModel = glm::scale(glm::translate(glm::mat4(1.0f), glm::vec3(0.0f, -1.0f, -5.0f)), glm::vec3(10.0f, 0.01f, 10.0f));
    glUniformMatrix4fv(locModel, 1, GL_FALSE, &groundModel[0][0]);
    glUniform3f(locColor, 0.4f, 0.4f, 0.45f);

    // draw a simple ground using immediate-like vertex attributes
    // create temporary VAO/VBO
    static unsigned int groundVAO = 0, groundVBO = 0;
    if (!groundVAO) {
        float verts[] = {
            -1.0f, 0.0f, -1.0f,
             1.0f, 0.0f, -1.0f,
             1.0f, 0.0f,  1.0f,
            -1.0f, 0.0f,  1.0f
        };
        float norms[] = {0,1,0, 0,1,0, 0,1,0, 0,1,0};
        unsigned int groundIdx[] = {0,1,2, 2,3,0};
        glGenVertexArrays(1, &groundVAO);
        glGenBuffers(1, &groundVBO);
        unsigned int ebo;
        glGenBuffers(1, &ebo);
        glBindVertexArray(groundVAO);
        glBindBuffer(GL_ARRAY_BUFFER, groundVBO);
        glBufferData(GL_ARRAY_BUFFER, sizeof(verts) + sizeof(norms), nullptr, GL_STATIC_DRAW);
        glBufferSubData(GL_ARRAY_BUFFER, 0, sizeof(verts), verts);
        glBufferSubData(GL_ARRAY_BUFFER, sizeof(verts), sizeof(norms), norms);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, ebo);
        glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(groundIdx), groundIdx, GL_STATIC_DRAW);
        glEnableVertexAttribArray(0);
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 0, (void*)0);
        glEnableVertexAttribArray(1);
        glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 0, (void*)(sizeof(verts)));
        glBindVertexArray(0);
    }
    pglBindVertexArray(groundVAO);
    glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);
    pglBindVertexArray(0);

    // draw spheres
    pglBindVertexArray(m_vao);
    for (const auto &b : world.bodies()) {
        glm::mat4 model = glm::translate(glm::mat4(1.0f), b.position) * glm::scale(glm::mat4(1.0f), glm::vec3(b.radius));
        pglUniformMatrix4fv(locModel, 1, GL_FALSE, &model[0][0]);
        pglUniform3f(locColor, 0.8f, 0.2f, 0.1f);
        pglDrawElements(GL_TRIANGLES, m_indexCount, GL_UNSIGNED_INT, 0);
    }

    pglBindVertexArray(0);
    pglUseProgram(0);
}
