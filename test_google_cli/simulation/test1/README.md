WorldSim - minimal C++ OpenGL world simulation

Requirements:
- CMake
- A working OpenGL development setup (GL headers/libs)
- GLFW 3 development package
- glm

Build:

```bash
mkdir build && cd build
cmake ..
make
./worldsim
```

Notes:
- Uses legacy fixed-function OpenGL for simplicity (GL/gl.h, GL/glext.h). This keeps the dependency surface small and avoids writing shaders.
- Only GLFW, GL and glm are used plus the standard library.
