# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/test_google_cli/simulation/test1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/test_google_cli/simulation/test1/build

# Include any dependencies generated for this target.
include CMakeFiles/worldsim.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/worldsim.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/worldsim.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/worldsim.dir/flags.make

CMakeFiles/worldsim.dir/src/main.cpp.o: CMakeFiles/worldsim.dir/flags.make
CMakeFiles/worldsim.dir/src/main.cpp.o: /home/<USER>/test_google_cli/simulation/test1/src/main.cpp
CMakeFiles/worldsim.dir/src/main.cpp.o: CMakeFiles/worldsim.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/simulation/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/worldsim.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/worldsim.dir/src/main.cpp.o -MF CMakeFiles/worldsim.dir/src/main.cpp.o.d -o CMakeFiles/worldsim.dir/src/main.cpp.o -c /home/<USER>/test_google_cli/simulation/test1/src/main.cpp

CMakeFiles/worldsim.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/worldsim.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/simulation/test1/src/main.cpp > CMakeFiles/worldsim.dir/src/main.cpp.i

CMakeFiles/worldsim.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/worldsim.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/simulation/test1/src/main.cpp -o CMakeFiles/worldsim.dir/src/main.cpp.s

CMakeFiles/worldsim.dir/src/World.cpp.o: CMakeFiles/worldsim.dir/flags.make
CMakeFiles/worldsim.dir/src/World.cpp.o: /home/<USER>/test_google_cli/simulation/test1/src/World.cpp
CMakeFiles/worldsim.dir/src/World.cpp.o: CMakeFiles/worldsim.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/simulation/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/worldsim.dir/src/World.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/worldsim.dir/src/World.cpp.o -MF CMakeFiles/worldsim.dir/src/World.cpp.o.d -o CMakeFiles/worldsim.dir/src/World.cpp.o -c /home/<USER>/test_google_cli/simulation/test1/src/World.cpp

CMakeFiles/worldsim.dir/src/World.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/worldsim.dir/src/World.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/simulation/test1/src/World.cpp > CMakeFiles/worldsim.dir/src/World.cpp.i

CMakeFiles/worldsim.dir/src/World.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/worldsim.dir/src/World.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/simulation/test1/src/World.cpp -o CMakeFiles/worldsim.dir/src/World.cpp.s

CMakeFiles/worldsim.dir/src/Renderer.cpp.o: CMakeFiles/worldsim.dir/flags.make
CMakeFiles/worldsim.dir/src/Renderer.cpp.o: /home/<USER>/test_google_cli/simulation/test1/src/Renderer.cpp
CMakeFiles/worldsim.dir/src/Renderer.cpp.o: CMakeFiles/worldsim.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/test_google_cli/simulation/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/worldsim.dir/src/Renderer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/worldsim.dir/src/Renderer.cpp.o -MF CMakeFiles/worldsim.dir/src/Renderer.cpp.o.d -o CMakeFiles/worldsim.dir/src/Renderer.cpp.o -c /home/<USER>/test_google_cli/simulation/test1/src/Renderer.cpp

CMakeFiles/worldsim.dir/src/Renderer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/worldsim.dir/src/Renderer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/test_google_cli/simulation/test1/src/Renderer.cpp > CMakeFiles/worldsim.dir/src/Renderer.cpp.i

CMakeFiles/worldsim.dir/src/Renderer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/worldsim.dir/src/Renderer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/test_google_cli/simulation/test1/src/Renderer.cpp -o CMakeFiles/worldsim.dir/src/Renderer.cpp.s

# Object files for target worldsim
worldsim_OBJECTS = \
"CMakeFiles/worldsim.dir/src/main.cpp.o" \
"CMakeFiles/worldsim.dir/src/World.cpp.o" \
"CMakeFiles/worldsim.dir/src/Renderer.cpp.o"

# External object files for target worldsim
worldsim_EXTERNAL_OBJECTS =

worldsim: CMakeFiles/worldsim.dir/src/main.cpp.o
worldsim: CMakeFiles/worldsim.dir/src/World.cpp.o
worldsim: CMakeFiles/worldsim.dir/src/Renderer.cpp.o
worldsim: CMakeFiles/worldsim.dir/build.make
worldsim: /usr/lib/x86_64-linux-gnu/libGL.so
worldsim: /usr/lib/x86_64-linux-gnu/libGLU.so
worldsim: CMakeFiles/worldsim.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/test_google_cli/simulation/test1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable worldsim"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/worldsim.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/worldsim.dir/build: worldsim
.PHONY : CMakeFiles/worldsim.dir/build

CMakeFiles/worldsim.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/worldsim.dir/cmake_clean.cmake
.PHONY : CMakeFiles/worldsim.dir/clean

CMakeFiles/worldsim.dir/depend:
	cd /home/<USER>/test_google_cli/simulation/test1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/test_google_cli/simulation/test1 /home/<USER>/test_google_cli/simulation/test1 /home/<USER>/test_google_cli/simulation/test1/build /home/<USER>/test_google_cli/simulation/test1/build /home/<USER>/test_google_cli/simulation/test1/build/CMakeFiles/worldsim.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/worldsim.dir/depend

